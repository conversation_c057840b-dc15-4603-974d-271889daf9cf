#!/usr/bin/env python3
"""
Character Recognition Model Training Script

This script trains a CNN model for recognizing alphanumeric characters (0-9, A-Z).
The model is designed to work with the dataset structure in the dataset folder.

Usage:
    python train_character_recognition.py

Features:
    - Supports both 'data' and 'data2' dataset versions
    - Configurable hyperparameters
    - Training progress visualization
    - Model checkpointing
    - Comprehensive evaluation metrics
"""

import os
import sys
import time
import json
import random
from pathlib import Path
from typing import Dict, List, Tuple, Optional

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader, random_split
import torchvision.transforms as transforms
from torchvision.datasets import ImageFolder

import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt
from sklearn.metrics import classification_report, confusion_matrix
import seaborn as sns

# Set random seeds for reproducibility
torch.manual_seed(42)
np.random.seed(42)
random.seed(42)

class Config:
    """Training configuration"""
    # Dataset settings
    DATASET_VERSION = "data2"  # Choose between "data" and "data2"
    DATASET_ROOT = "dataset"
    IMAGE_SIZE = (32, 32)  # Resize images to this size

    # Training settings
    BATCH_SIZE = 64
    LEARNING_RATE = 0.001
    NUM_EPOCHS = 50
    VALIDATION_SPLIT = 0.2  # 20% of training data for validation

    # Model settings
    NUM_CLASSES = 36  # 0-9 (10) + A-Z (26)
    DROPOUT_RATE = 0.5

    # Hardware settings
    DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    NUM_WORKERS = 4 if torch.cuda.is_available() else 0

    # Output settings
    MODEL_SAVE_PATH = "model/character_recognition_model.pth"
    RESULTS_DIR = "training_results"

    # Early stopping
    PATIENCE = 10  # Stop if no improvement for this many epochs
    MIN_DELTA = 0.001  # Minimum change to qualify as improvement

class CharacterDataset(Dataset):
    """Custom dataset for character recognition"""

    def __init__(self, root_dir: str, split: str = "training_data", transform=None):
        """
        Args:
            root_dir: Path to dataset root
            split: Either "training_data" or "testing_data"
            transform: Optional transform to be applied on images
        """
        self.root_dir = Path(root_dir)
        self.split = split
        self.transform = transform

        # Character classes (0-9, A-Z)
        self.classes = [str(i) for i in range(10)] + [chr(i) for i in range(ord('A'), ord('Z') + 1)]
        self.class_to_idx = {cls: idx for idx, cls in enumerate(self.classes)}

        # Load all image paths and labels
        self.samples = []
        self._load_samples()

    def _load_samples(self):
        """Load all image paths and their corresponding labels"""
        split_dir = self.root_dir / self.split

        for class_name in self.classes:
            class_dir = split_dir / class_name
            if class_dir.exists():
                for img_path in class_dir.glob("*.png"):
                    self.samples.append((str(img_path), self.class_to_idx[class_name]))

        print(f"Loaded {len(self.samples)} samples from {split_dir}")

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        img_path, label = self.samples[idx]

        # Load image
        image = Image.open(img_path).convert('RGB')

        if self.transform:
            image = self.transform(image)

        return image, label

class CharacterCNN(nn.Module):
    """CNN model for character recognition"""

    def __init__(self, num_classes: int = 36, dropout_rate: float = 0.5):
        super(CharacterCNN, self).__init__()

        # Convolutional layers
        self.conv1 = nn.Conv2d(3, 32, kernel_size=3, padding=1)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=3, padding=1)
        self.conv3 = nn.Conv2d(64, 128, kernel_size=3, padding=1)
        self.conv4 = nn.Conv2d(128, 256, kernel_size=3, padding=1)

        # Batch normalization
        self.bn1 = nn.BatchNorm2d(32)
        self.bn2 = nn.BatchNorm2d(64)
        self.bn3 = nn.BatchNorm2d(128)
        self.bn4 = nn.BatchNorm2d(256)

        # Pooling
        self.pool = nn.MaxPool2d(2, 2)

        # Dropout
        self.dropout = nn.Dropout(dropout_rate)

        # Fully connected layers
        # After 4 pooling operations: 32x32 -> 16x16 -> 8x8 -> 4x4 -> 2x2
        self.fc1 = nn.Linear(256 * 2 * 2, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, num_classes)

    def forward(self, x):
        # Convolutional layers with batch norm, ReLU, and pooling
        x = self.pool(F.relu(self.bn1(self.conv1(x))))
        x = self.pool(F.relu(self.bn2(self.conv2(x))))
        x = self.pool(F.relu(self.bn3(self.conv3(x))))
        x = self.pool(F.relu(self.bn4(self.conv4(x))))

        # Flatten
        x = x.view(-1, 256 * 2 * 2)

        # Fully connected layers with dropout
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)

        return x

class EarlyStopping:
    """Early stopping to prevent overfitting"""

    def __init__(self, patience: int = 10, min_delta: float = 0.001):
        self.patience = patience
        self.min_delta = min_delta
        self.counter = 0
        self.best_loss = float('inf')

    def __call__(self, val_loss: float) -> bool:
        if val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0
        else:
            self.counter += 1

        return self.counter >= self.patience

def get_transforms():
    """Get data transforms for training and validation"""
    train_transform = transforms.Compose([
        transforms.Resize(Config.IMAGE_SIZE),
        transforms.RandomRotation(10),
        transforms.RandomAffine(degrees=0, translate=(0.1, 0.1)),
        transforms.ColorJitter(brightness=0.2, contrast=0.2),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    val_transform = transforms.Compose([
        transforms.Resize(Config.IMAGE_SIZE),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    return train_transform, val_transform

def create_data_loaders():
    """Create training, validation, and test data loaders"""
    train_transform, val_transform = get_transforms()

    # Load training dataset
    dataset_path = os.path.join(Config.DATASET_ROOT, Config.DATASET_VERSION)
    train_dataset = CharacterDataset(dataset_path, "training_data", train_transform)

    # Split training data into train and validation
    train_size = int((1 - Config.VALIDATION_SPLIT) * len(train_dataset))
    val_size = len(train_dataset) - train_size
    train_subset, val_subset = random_split(train_dataset, [train_size, val_size])

    # Update validation subset transform
    val_dataset = CharacterDataset(dataset_path, "training_data", val_transform)
    val_subset.dataset = val_dataset

    # Load test dataset
    test_dataset = CharacterDataset(dataset_path, "testing_data", val_transform)

    # Create data loaders
    train_loader = DataLoader(
        train_subset,
        batch_size=Config.BATCH_SIZE,
        shuffle=True,
        num_workers=Config.NUM_WORKERS
    )

    val_loader = DataLoader(
        val_subset,
        batch_size=Config.BATCH_SIZE,
        shuffle=False,
        num_workers=Config.NUM_WORKERS
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=Config.BATCH_SIZE,
        shuffle=False,
        num_workers=Config.NUM_WORKERS
    )

    return train_loader, val_loader, test_loader

def train_epoch(model, train_loader, criterion, optimizer, device):
    """Train for one epoch"""
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0

    for batch_idx, (data, target) in enumerate(train_loader):
        data, target = data.to(device), target.to(device)

        optimizer.zero_grad()
        output = model(data)
        loss = criterion(output, target)
        loss.backward()
        optimizer.step()

        running_loss += loss.item()
        _, predicted = output.max(1)
        total += target.size(0)
        correct += predicted.eq(target).sum().item()

        if batch_idx % 100 == 0:
            print(f'Batch {batch_idx}/{len(train_loader)}, '
                  f'Loss: {loss.item():.4f}, '
                  f'Acc: {100.*correct/total:.2f}%')

    epoch_loss = running_loss / len(train_loader)
    epoch_acc = 100. * correct / total

    return epoch_loss, epoch_acc

def validate_epoch(model, val_loader, criterion, device):
    """Validate for one epoch"""
    model.eval()
    running_loss = 0.0
    correct = 0
    total = 0

    with torch.no_grad():
        for data, target in val_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            loss = criterion(output, target)

            running_loss += loss.item()
            _, predicted = output.max(1)
            total += target.size(0)
            correct += predicted.eq(target).sum().item()

    epoch_loss = running_loss / len(val_loader)
    epoch_acc = 100. * correct / total

    return epoch_loss, epoch_acc

def test_model(model, test_loader, device, classes):
    """Test the model and generate detailed metrics"""
    model.eval()
    all_predictions = []
    all_targets = []
    correct = 0
    total = 0

    with torch.no_grad():
        for data, target in test_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            _, predicted = output.max(1)

            all_predictions.extend(predicted.cpu().numpy())
            all_targets.extend(target.cpu().numpy())

            total += target.size(0)
            correct += predicted.eq(target).sum().item()

    accuracy = 100. * correct / total

    # Generate classification report
    class_names = [str(i) for i in range(10)] + [chr(i) for i in range(ord('A'), ord('Z') + 1)]
    report = classification_report(all_targets, all_predictions, target_names=class_names)

    # Generate confusion matrix
    cm = confusion_matrix(all_targets, all_predictions)

    return accuracy, report, cm, all_predictions, all_targets

def plot_training_history(train_losses, val_losses, train_accs, val_accs, save_path):
    """Plot training history"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))

    # Plot losses
    ax1.plot(train_losses, label='Training Loss', color='blue')
    ax1.plot(val_losses, label='Validation Loss', color='red')
    ax1.set_title('Training and Validation Loss')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.legend()
    ax1.grid(True)

    # Plot accuracies
    ax2.plot(train_accs, label='Training Accuracy', color='blue')
    ax2.plot(val_accs, label='Validation Accuracy', color='red')
    ax2.set_title('Training and Validation Accuracy')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Accuracy (%)')
    ax2.legend()
    ax2.grid(True)

    plt.tight_layout()
    plt.savefig(save_path)
    plt.close()

def plot_confusion_matrix(cm, classes, save_path):
    """Plot confusion matrix"""
    plt.figure(figsize=(12, 10))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=classes, yticklabels=classes)
    plt.title('Confusion Matrix')
    plt.xlabel('Predicted')
    plt.ylabel('Actual')
    plt.tight_layout()
    plt.savefig(save_path)
    plt.close()

def save_model_and_config(model, config_dict, model_path):
    """Save model weights and configuration"""
    # Create model directory if it doesn't exist
    os.makedirs(os.path.dirname(model_path), exist_ok=True)

    # Save model state dict
    torch.save({
        'model_state_dict': model.state_dict(),
        'config': config_dict,
        'classes': [str(i) for i in range(10)] + [chr(i) for i in range(ord('A'), ord('Z') + 1)]
    }, model_path)

    print(f"Model saved to {model_path}")

def load_model(model_path, device):
    """Load trained model"""
    checkpoint = torch.load(model_path, map_location=device)

    model = CharacterCNN(
        num_classes=checkpoint['config']['num_classes'],
        dropout_rate=checkpoint['config']['dropout_rate']
    )
    model.load_state_dict(checkpoint['model_state_dict'])
    model.to(device)

    return model, checkpoint['classes']

def visualize_sample_predictions(model, test_loader, device, classes, save_path, num_samples=16):
    """Visualize sample predictions"""
    model.eval()

    # Get a batch of test data
    data_iter = iter(test_loader)
    images, labels = next(data_iter)
    images, labels = images.to(device), labels.to(device)

    with torch.no_grad():
        outputs = model(images)
        _, predictions = torch.max(outputs, 1)

    # Convert to CPU and denormalize images
    images = images.cpu()
    mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
    std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
    images = images * std + mean
    images = torch.clamp(images, 0, 1)

    # Plot samples
    fig, axes = plt.subplots(4, 4, figsize=(12, 12))
    for i in range(min(num_samples, len(images))):
        ax = axes[i // 4, i % 4]

        # Convert to numpy and transpose for matplotlib
        img = images[i].permute(1, 2, 0).numpy()
        ax.imshow(img)

        true_label = classes[labels[i]]
        pred_label = classes[predictions[i]]
        color = 'green' if true_label == pred_label else 'red'

        ax.set_title(f'True: {true_label}, Pred: {pred_label}', color=color)
        ax.axis('off')

    plt.tight_layout()
    plt.savefig(save_path)
    plt.close()

def main():
    """Main training function"""
    print("Starting Character Recognition Training")
    print(f"Device: {Config.DEVICE}")
    print(f"Dataset: {Config.DATASET_VERSION}")
    print(f"Batch size: {Config.BATCH_SIZE}")
    print(f"Learning rate: {Config.LEARNING_RATE}")
    print(f"Number of epochs: {Config.NUM_EPOCHS}")
    print("-" * 50)

    # Create results directory
    os.makedirs(Config.RESULTS_DIR, exist_ok=True)

    # Create data loaders
    print("Loading datasets...")
    train_loader, val_loader, test_loader = create_data_loaders()

    # Create model
    print("Creating model...")
    model = CharacterCNN(Config.NUM_CLASSES, Config.DROPOUT_RATE)
    model.to(Config.DEVICE)

    # Print model summary
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")

    # Loss function and optimizer
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=Config.LEARNING_RATE)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'min', patience=5, factor=0.5)

    # Early stopping
    early_stopping = EarlyStopping(Config.PATIENCE, Config.MIN_DELTA)

    # Training history
    train_losses = []
    val_losses = []
    train_accs = []
    val_accs = []

    best_val_acc = 0.0

    print("\nStarting training...")
    start_time = time.time()

    for epoch in range(Config.NUM_EPOCHS):
        print(f"\nEpoch {epoch+1}/{Config.NUM_EPOCHS}")
        print("-" * 30)

        # Train
        train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, Config.DEVICE)

        # Validate
        val_loss, val_acc = validate_epoch(model, val_loader, criterion, Config.DEVICE)

        # Update learning rate
        scheduler.step(val_loss)

        # Save history
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        train_accs.append(train_acc)
        val_accs.append(val_acc)

        print(f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%")
        print(f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%")
        print(f"Learning Rate: {optimizer.param_groups[0]['lr']:.6f}")

        # Save best model
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            config_dict = {
                'num_classes': Config.NUM_CLASSES,
                'dropout_rate': Config.DROPOUT_RATE,
                'image_size': Config.IMAGE_SIZE,
                'best_val_acc': best_val_acc
            }
            save_model_and_config(model, config_dict, Config.MODEL_SAVE_PATH)
            print(f"New best model saved! Val Acc: {val_acc:.2f}%")

        # Early stopping check
        if early_stopping(val_loss):
            print(f"Early stopping triggered after {epoch+1} epochs")
            break

    training_time = time.time() - start_time
    print(f"\nTraining completed in {training_time:.2f} seconds")
    print(f"Best validation accuracy: {best_val_acc:.2f}%")

    # Plot training history
    plot_path = os.path.join(Config.RESULTS_DIR, "training_history.png")
    plot_training_history(train_losses, val_losses, train_accs, val_accs, plot_path)
    print(f"Training history saved to {plot_path}")

    # Load best model for testing
    print("\nLoading best model for testing...")
    model, classes = load_model(Config.MODEL_SAVE_PATH, Config.DEVICE)

    # Test the model
    print("Testing model...")
    test_acc, report, cm, predictions, targets = test_model(model, test_loader, Config.DEVICE, classes)

    print(f"\nTest Accuracy: {test_acc:.2f}%")
    print("\nClassification Report:")
    print(report)

    # Save results
    results = {
        'test_accuracy': test_acc,
        'best_val_accuracy': best_val_acc,
        'training_time': training_time,
        'num_epochs_trained': len(train_losses),
        'classification_report': report
    }

    results_path = os.path.join(Config.RESULTS_DIR, "results.json")
    with open(results_path, 'w') as f:
        json.dump(results, f, indent=2)

    # Plot confusion matrix
    cm_path = os.path.join(Config.RESULTS_DIR, "confusion_matrix.png")
    plot_confusion_matrix(cm, classes, cm_path)
    print(f"Confusion matrix saved to {cm_path}")

    # Visualize sample predictions
    samples_path = os.path.join(Config.RESULTS_DIR, "sample_predictions.png")
    visualize_sample_predictions(model, test_loader, Config.DEVICE, classes, samples_path)
    print(f"Sample predictions saved to {samples_path}")

    print(f"\nAll results saved to {Config.RESULTS_DIR}")
    print("Training completed successfully!")

if __name__ == "__main__":
    main()
