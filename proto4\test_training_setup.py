#!/usr/bin/env python3
"""
Test script to verify the training setup is working correctly.
This script performs basic checks before running the full training.
"""

import os
import sys
from pathlib import Path
import importlib.util

def check_python_version():
    """Check if Python version is compatible"""
    print("Checking Python version...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 7:
        print(f"✓ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"✗ Python {version.major}.{version.minor}.{version.micro} is too old. Need Python 3.7+")
        return False

def check_dependencies():
    """Check if required packages are available"""
    print("\nChecking dependencies...")
    required_packages = [
        'torch',
        'torchvision', 
        'numpy',
        'PIL',
        'matplotlib',
        'sklearn',
        'seaborn'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
            elif package == 'sklearn':
                import sklearn
            else:
                importlib.import_module(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("Run: pip install -r requirements.txt")
        return False
    else:
        print("✓ All dependencies are available")
        return True

def check_dataset_structure():
    """Check if dataset structure is correct"""
    print("\nChecking dataset structure...")
    
    dataset_root = Path("dataset")
    if not dataset_root.exists():
        print("✗ Dataset folder not found")
        return False
    
    # Check for at least one dataset version
    data_versions = ["data", "data2"]
    found_version = None
    
    for version in data_versions:
        version_path = dataset_root / version
        if version_path.exists():
            found_version = version
            break
    
    if not found_version:
        print("✗ No dataset version found (data or data2)")
        return False
    
    print(f"✓ Found dataset version: {found_version}")
    
    # Check training and testing data
    version_path = dataset_root / found_version
    train_path = version_path / "training_data"
    test_path = version_path / "testing_data"
    
    if not train_path.exists():
        print("✗ training_data folder not found")
        return False
    
    if not test_path.exists():
        print("✗ testing_data folder not found")
        return False
    
    print("✓ Training and testing folders found")
    
    # Check for character classes
    expected_classes = [str(i) for i in range(10)] + [chr(i) for i in range(ord('A'), ord('Z') + 1)]
    
    train_classes = [d.name for d in train_path.iterdir() if d.is_dir()]
    test_classes = [d.name for d in test_path.iterdir() if d.is_dir()]
    
    print(f"✓ Found {len(train_classes)} training classes")
    print(f"✓ Found {len(test_classes)} testing classes")
    
    # Count sample images
    total_train_images = 0
    total_test_images = 0
    
    for class_dir in train_path.iterdir():
        if class_dir.is_dir():
            images = list(class_dir.glob("*.png"))
            total_train_images += len(images)
    
    for class_dir in test_path.iterdir():
        if class_dir.is_dir():
            images = list(class_dir.glob("*.png"))
            total_test_images += len(images)
    
    print(f"✓ Total training images: {total_train_images:,}")
    print(f"✓ Total testing images: {total_test_images:,}")
    
    if total_train_images == 0:
        print("✗ No training images found")
        return False
    
    if total_test_images == 0:
        print("✗ No testing images found")
        return False
    
    return True

def check_gpu_availability():
    """Check if GPU is available for training"""
    print("\nChecking GPU availability...")
    
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            print(f"✓ GPU available: {gpu_name}")
            print(f"✓ Number of GPUs: {gpu_count}")
            return True
        else:
            print("⚠ No GPU available - training will use CPU (slower)")
            return True
    except ImportError:
        print("✗ Cannot check GPU - torch not installed")
        return False

def check_disk_space():
    """Check available disk space"""
    print("\nChecking disk space...")
    
    try:
        import shutil
        total, used, free = shutil.disk_usage(".")
        free_gb = free // (1024**3)
        
        if free_gb >= 2:
            print(f"✓ Available disk space: {free_gb} GB")
            return True
        else:
            print(f"⚠ Low disk space: {free_gb} GB (recommended: 2+ GB)")
            return True
    except Exception as e:
        print(f"⚠ Cannot check disk space: {e}")
        return True

def test_model_creation():
    """Test if model can be created"""
    print("\nTesting model creation...")
    
    try:
        from train_character_recognition import CharacterCNN
        import torch
        
        model = CharacterCNN(num_classes=36, dropout_rate=0.5)
        
        # Test forward pass
        dummy_input = torch.randn(1, 3, 32, 32)
        output = model(dummy_input)
        
        if output.shape == (1, 36):
            print("✓ Model creation and forward pass successful")
            return True
        else:
            print(f"✗ Unexpected output shape: {output.shape}")
            return False
            
    except Exception as e:
        print(f"✗ Model creation failed: {e}")
        return False

def main():
    """Run all checks"""
    print("Character Recognition Training Setup Test")
    print("=" * 50)
    
    checks = [
        ("Python Version", check_python_version),
        ("Dependencies", check_dependencies),
        ("Dataset Structure", check_dataset_structure),
        ("GPU Availability", check_gpu_availability),
        ("Disk Space", check_disk_space),
        ("Model Creation", test_model_creation)
    ]
    
    passed = 0
    total = len(checks)
    
    for name, check_func in checks:
        try:
            if check_func():
                passed += 1
        except Exception as e:
            print(f"✗ {name} check failed with error: {e}")
    
    print("\n" + "=" * 50)
    print(f"Setup Check Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All checks passed! You're ready to start training.")
        print("\nTo start training, run:")
        print("  python run_training.py")
        print("  or")
        print("  python train_character_recognition.py")
    else:
        print("⚠ Some checks failed. Please fix the issues above before training.")
        
        if passed >= total - 2:
            print("Most checks passed - you can probably still run training.")

if __name__ == "__main__":
    main()
