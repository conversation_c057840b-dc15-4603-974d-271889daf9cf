# Character Recognition Model Training

This directory contains a complete training pipeline for a CNN-based character recognition model that can recognize alphanumeric characters (0-9, A-Z).

## 📁 Dataset Structure

Your dataset should be organized as follows:
```
dataset/
├── data/                    # First dataset version
│   ├── training_data/
│   │   ├── 0/              # Images of digit '0'
│   │   ├── 1/              # Images of digit '1'
│   │   ├── ...
│   │   ├── A/              # Images of letter 'A'
│   │   ├── B/              # Images of letter 'B'
│   │   └── ...
│   └── testing_data/
│       ├── 0/
│       ├── 1/
│       └── ...
└── data2/                   # Second dataset version (same structure)
    ├── training_data/
    └── testing_data/
```

## 🚀 Quick Start

### Option 1: Using the Training Runner (Recommended)
```bash
python run_training.py
```
This interactive script will:
- Install required dependencies
- Let you choose from predefined configurations
- Run training with your selected parameters

### Option 2: Direct Training
```bash
# Install dependencies first
pip install -r requirements.txt

# Run training
python train_character_recognition.py
```

## ⚙️ Configuration

You can modify training parameters by editing the `Config` class in `train_character_recognition.py`:

```python
class Config:
    # Dataset settings
    DATASET_VERSION = "data2"        # Choose "data" or "data2"
    IMAGE_SIZE = (32, 32)           # Input image size
    
    # Training settings
    BATCH_SIZE = 64                 # Batch size for training
    LEARNING_RATE = 0.001           # Learning rate
    NUM_EPOCHS = 50                 # Maximum number of epochs
    VALIDATION_SPLIT = 0.2          # 20% for validation
    
    # Model settings
    NUM_CLASSES = 36                # 0-9 (10) + A-Z (26)
    DROPOUT_RATE = 0.5              # Dropout for regularization
    
    # Early stopping
    PATIENCE = 10                   # Stop if no improvement
    MIN_DELTA = 0.001              # Minimum improvement threshold
```

## 🏗️ Model Architecture

The CNN model includes:
- **4 Convolutional layers** with batch normalization
- **Max pooling** after each conv layer
- **3 Fully connected layers** with dropout
- **36 output classes** (0-9, A-Z)

**Model Summary:**
- Input: 32x32x3 RGB images
- ~1.5M trainable parameters
- Output: 36-class probability distribution

## 📊 Training Features

### Data Augmentation
- Random rotation (±10°)
- Random translation (±10%)
- Color jitter (brightness/contrast)
- Normalization with ImageNet stats

### Training Techniques
- **Adam optimizer** with learning rate scheduling
- **Cross-entropy loss** for classification
- **Early stopping** to prevent overfitting
- **Model checkpointing** (saves best model)
- **Learning rate reduction** on plateau

### Monitoring & Visualization
- Real-time training progress
- Training/validation loss and accuracy plots
- Confusion matrix visualization
- Sample prediction visualization
- Comprehensive classification report

## 📈 Output Files

After training, you'll find these files:

```
model/
└── character_recognition_model.pth    # Trained model weights

training_results/
├── training_history.png              # Loss/accuracy plots
├── confusion_matrix.png              # Confusion matrix
├── sample_predictions.png            # Sample predictions
└── results.json                      # Training metrics
```

## 🔧 Hardware Requirements

- **CPU**: Any modern CPU (training will be slower)
- **GPU**: CUDA-compatible GPU recommended for faster training
- **RAM**: 8GB+ recommended
- **Storage**: 2GB+ free space for model and results

## 📋 Dependencies

Required packages (automatically installed):
- `torch` & `torchvision` - Deep learning framework
- `matplotlib` & `seaborn` - Visualization
- `scikit-learn` - Metrics and evaluation
- `pillow` - Image processing
- `numpy` - Numerical operations

## 🎯 Expected Performance

With the provided dataset:
- **Training time**: 10-30 minutes (depending on hardware)
- **Expected accuracy**: 85-95% on test set
- **Model size**: ~6MB

## 🔍 Troubleshooting

### Common Issues:

1. **CUDA out of memory**
   - Reduce `BATCH_SIZE` in config (try 32 or 16)
   - Reduce `IMAGE_SIZE` to (28, 28)

2. **Training too slow**
   - Reduce `NUM_EPOCHS` for testing
   - Use GPU if available
   - Increase `BATCH_SIZE` if you have enough memory

3. **Low accuracy**
   - Increase `NUM_EPOCHS`
   - Try different learning rates (0.0001 - 0.01)
   - Check dataset quality and balance

4. **Import errors**
   - Run: `pip install -r requirements.txt`
   - Make sure you're in the correct virtual environment

## 📝 Usage Examples

### Quick Test (5 epochs)
```python
# Edit Config class:
NUM_EPOCHS = 5
BATCH_SIZE = 32
```

### High Accuracy Training
```python
# Edit Config class:
NUM_EPOCHS = 100
LEARNING_RATE = 0.0005
PATIENCE = 15
```

### Memory-Efficient Training
```python
# Edit Config class:
BATCH_SIZE = 16
IMAGE_SIZE = (28, 28)
```

## 🔄 Using the Trained Model

After training, you can load and use the model:

```python
from train_character_recognition import load_model, CharacterCNN
import torch

# Load trained model
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model, classes = load_model("model/character_recognition_model.pth", device)

# Use for inference
model.eval()
with torch.no_grad():
    prediction = model(input_tensor)
    predicted_class = classes[prediction.argmax()]
```

## 🤝 Support

If you encounter any issues:
1. Check the troubleshooting section above
2. Verify your dataset structure matches the expected format
3. Ensure all dependencies are installed correctly
4. Try reducing batch size or number of epochs for testing

Happy training! 🎉
