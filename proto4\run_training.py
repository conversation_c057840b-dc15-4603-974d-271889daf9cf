#!/usr/bin/env python3
"""
Simple script to run the character recognition training with different configurations.
This script allows you to easily modify training parameters without editing the main script.
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ All packages installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Error installing packages: {e}")
        return False

def run_training(dataset_version="data2", epochs=50, batch_size=64, learning_rate=0.001):
    """Run training with specified parameters"""
    print(f"Starting training with:")
    print(f"  Dataset: {dataset_version}")
    print(f"  Epochs: {epochs}")
    print(f"  Batch size: {batch_size}")
    print(f"  Learning rate: {learning_rate}")
    print("-" * 50)
    
    # Set environment variables to override config
    env = os.environ.copy()
    env['DATASET_VERSION'] = dataset_version
    env['NUM_EPOCHS'] = str(epochs)
    env['BATCH_SIZE'] = str(batch_size)
    env['LEARNING_RATE'] = str(learning_rate)
    
    try:
        subprocess.check_call([sys.executable, "train_character_recognition.py"], env=env)
        print("✓ Training completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Training failed: {e}")
        return False

def main():
    """Main function"""
    print("Character Recognition Training Runner")
    print("=" * 50)
    
    # Check if requirements are installed
    print("Checking requirements...")
    if not install_requirements():
        print("Please install requirements manually and try again.")
        return
    
    # Training configurations
    configs = [
        {
            "name": "Quick Test (5 epochs)",
            "dataset": "data2",
            "epochs": 5,
            "batch_size": 32,
            "lr": 0.001
        },
        {
            "name": "Standard Training (50 epochs)",
            "dataset": "data2", 
            "epochs": 50,
            "batch_size": 64,
            "lr": 0.001
        },
        {
            "name": "Long Training (100 epochs)",
            "dataset": "data2",
            "epochs": 100,
            "batch_size": 64,
            "lr": 0.0005
        },
        {
            "name": "Custom Configuration",
            "dataset": "custom",
            "epochs": 0,
            "batch_size": 0,
            "lr": 0
        }
    ]
    
    print("\nAvailable training configurations:")
    for i, config in enumerate(configs):
        print(f"{i+1}. {config['name']}")
    
    try:
        choice = int(input("\nSelect configuration (1-4): ")) - 1
        
        if choice < 0 or choice >= len(configs):
            print("Invalid choice!")
            return
        
        config = configs[choice]
        
        if config['name'] == "Custom Configuration":
            print("\nEnter custom parameters:")
            dataset = input("Dataset version (data/data2) [data2]: ").strip() or "data2"
            epochs = int(input("Number of epochs [50]: ") or "50")
            batch_size = int(input("Batch size [64]: ") or "64")
            lr = float(input("Learning rate [0.001]: ") or "0.001")
            
            run_training(dataset, epochs, batch_size, lr)
        else:
            run_training(config['dataset'], config['epochs'], config['batch_size'], config['lr'])
            
    except KeyboardInterrupt:
        print("\nTraining cancelled by user.")
    except ValueError:
        print("Invalid input! Please enter numbers only.")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
